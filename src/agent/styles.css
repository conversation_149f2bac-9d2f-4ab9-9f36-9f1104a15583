@import "tailwindcss";

/* Custom styles for SQL Agent UI components */

/* Base component styling */
.sql-agent-component {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Code blocks */
.sql-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Table styling */
.sql-results-table {
  border-collapse: collapse;
  width: 100%;
}

.sql-results-table th,
.sql-results-table td {
  text-align: left;
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
}

.sql-results-table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.sql-results-table tr:nth-child(even) {
  background-color: #f9fafb;
}

.sql-results-table tr:hover {
  background-color: #f3f4f6;
}

/* Status indicators */
.status-generated {
  background-color: #fef3c7;
  color: #92400e;
}

.status-executing {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-success {
  background-color: #d1fae5;
  color: #065f46;
}

.status-error {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive design */
@media (max-width: 768px) {
  .sql-results-table {
    font-size: 0.75rem;
  }
  
  .sql-results-table th,
  .sql-results-table td {
    padding: 6px 8px;
  }
}

/* Scrollable code blocks */
.code-scroll {
  max-height: 300px;
  overflow-y: auto;
}

/* Custom scrollbar */
.code-scroll::-webkit-scrollbar {
  width: 6px;
}

.code-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.code-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.code-scroll::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
